<?php

namespace Plugins\Business\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Fill;

class ContactSampleExport implements FromArray, WithHeadings, WithStyles, WithColumnWidths
{
    /**
     * Return sample data array
     */
    public function array(): array
    {
        return [
            [
                '<PERSON>',
                'Acme Corporation',
                '<EMAIL>',
                'CEO',
                'Executive',
                '<EMAIL>',
                '+1-555-0123',
                true,
                'Primary contact for all executive decisions'
            ],
            [
                '<PERSON>',
                'Acme Corporation',
                '<EMAIL>',
                'CTO',
                'Technology',
                '<EMAIL>',
                '+1-555-0124',
                false,
                'Technical lead for all IT projects'
            ],
            [
                '<PERSON>',
                'Tech Solutions Inc',
                '<EMAIL>',
                'Project Manager',
                'Operations',
                '<EMAIL>',
                '+1-555-0456',
                true,
                'Main point of contact for project coordination'
            ],
            [
                'Emily Davis',
                'Global Enterprises',
                '<EMAIL>',
                'Sales Director',
                'Sales',
                '<EMAIL>',
                '+44-20-1234-5678',
                true,
                'Handles all sales inquiries and partnerships'
            ]
        ];
    }

    /**
     * Return the headings for the Excel file
     */
    public function headings(): array
    {
        return [
            'name',
            'business_name',
            'business_email',
            'position',
            'department',
            'email',
            'phone',
            'is_primary',
            'notes'
        ];
    }

    /**
     * Apply styles to the worksheet
     */
    public function styles(Worksheet $sheet)
    {
        // Style the header row
        $sheet->getStyle('A1:I1')->applyFromArray([
            'font' => [
                'bold' => true,
                'color' => ['rgb' => 'FFFFFF'],
            ],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'startColor' => ['rgb' => '059669'], // Green color
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
                'vertical' => Alignment::VERTICAL_CENTER,
            ],
        ]);

        // Add borders to all cells with data
        $sheet->getStyle('A1:I5')->applyFromArray([
            'borders' => [
                'allBorders' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    'color' => ['rgb' => 'CCCCCC'],
                ],
            ],
        ]);

        // Center align all data
        $sheet->getStyle('A2:I5')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);
        $sheet->getStyle('A2:I5')->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);

        return $sheet;
    }

    /**
     * Set column widths
     */
    public function columnWidths(): array
    {
        return [
            'A' => 20, // name
            'B' => 20, // business_name
            'C' => 25, // business_email
            'D' => 20, // position
            'E' => 15, // department
            'F' => 25, // email
            'G' => 15, // phone
            'H' => 12, // is_primary
            'I' => 30, // notes
        ];
    }
}
