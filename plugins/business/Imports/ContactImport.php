<?php

namespace Plugins\Business\Imports;

use Plugins\Business\Models\Contact;
use Plugins\Business\Models\Business;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\SkipsErrors;
use Maatwebsite\Excel\Concerns\SkipsOnError;
use Maatwebsite\Excel\Concerns\SkipsFailures;
use Maatwebsite\Excel\Concerns\SkipsOnFailure;

class ContactImport implements ToModel, WithHeadingRow, WithValidation, SkipsOnError, SkipsOnFailure
{
    use Importable, SkipsErrors, SkipsFailures;

    private $importedCount = 0;
    private $skippedCount = 0;
    private $processedRows = 0;
    private $customErrors = [];

    /**
     * Transform a row into a model
     */
    public function model(array $row)
    {
        $this->processedRows++;

        // Skip empty rows
        if (empty(array_filter($row))) {
            return null;
        }

        // Validate required fields
        if (empty($row['name'])) {
            $this->customErrors[] = "Row " . ($this->processedRows + 1) . ": Missing required field (name).";
            return null;
        }

        try {
            // Find the business by name or email
            $business = null;

            if (!empty($row['business_name'])) {
                $business = Business::where('name', $row['business_name'])->first();
            }

            if (!$business && !empty($row['business_email'])) {
                $business = Business::where('email', $row['business_email'])->first();
            }

            if (!$business) {
                $this->customErrors[] = "Row " . ($this->processedRows + 1) . ": Business not found for contact '{$row['name']}'. Please ensure the business exists first.";
                return null;
            }

            // Check if contact already exists for this business
            $existingContact = Contact::where('business_id', $business->id)
                ->where('name', $row['name'])
                ->first();

            // If email is provided, also check by email
            if (!$existingContact && !empty($row['email'])) {
                $existingContact = Contact::where('business_id', $business->id)
                    ->where('email', $row['email'])
                    ->first();
            }

            if ($existingContact) {
                // Skip existing contacts silently (don't add to errors)
                $this->skippedCount++;
                return null;
            }

            // Handle primary contact logic
            $isPrimary = filter_var($row['is_primary'] ?? false, FILTER_VALIDATE_BOOLEAN);

            // If this contact is set as primary, unset other primary contacts for this business
            if ($isPrimary) {
                Contact::where('business_id', $business->id)
                    ->where('is_primary', true)
                    ->update(['is_primary' => false]);
            }

            $contact = new Contact([
                'business_id' => $business->id,
                'name' => $row['name'],
                'position' => $row['position'] ?? null,
                'department' => $row['department'] ?? null,
                'email' => $row['email'] ?? null,
                'phone' => $row['phone'] ?? null,
                'phone_ext' => $row['phone_ext'] ?? null,
                'phone2' => $row['phone2'] ?? null,
                'phone2_ext' => $row['phone2_ext'] ?? null,
                'is_primary' => $isPrimary,
                'notes' => $row['notes'] ?? null,
            ]);

            $this->importedCount++;
            return $contact;

        } catch (\Exception $e) {
            $this->customErrors[] = "Row " . ($this->processedRows + 1) . ": " . $e->getMessage();
            return null;
        }
    }

    /**
     * Validation rules for each row
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'business_name' => 'required_without:business_email|string|max:255',
            'business_email' => 'required_without:business_name|email|max:255',
            'position' => 'nullable|string|max:255',
            'department' => 'nullable|string|max:255',
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:20',
            'phone_ext' => 'nullable|max:10',
            'phone2' => 'nullable|string|max:20',
            'phone2_ext' => 'nullable|max:10',
            'is_primary' => 'nullable|boolean',
            'notes' => 'nullable|string',
        ];
    }

    /**
     * Custom attributes for validation error messages
     */
    public function customValidationAttributes(): array
    {
        return [
            'name' => 'Contact Name',
            'business_name' => 'Business Name',
            'business_email' => 'Business Email',
            'position' => 'Position',
            'department' => 'Department',
            'email' => 'Email Address',
            'phone' => 'Phone Number',
            'is_primary' => 'Primary Contact',
            'notes' => 'Notes',
        ];
    }

    /**
     * Get the count of imported records
     */
    public function getImportedCount(): int
    {
        return $this->importedCount;
    }

    /**
     * Get the count of skipped records
     */
    public function getSkippedCount(): int
    {
        return $this->skippedCount;
    }

    /**
     * Get the count of processed rows
     */
    public function getProcessedRows(): int
    {
        return $this->processedRows;
    }

    /**
     * Get import errors
     */
    public function getErrors(): array
    {
        // Combine validation failures and custom errors
        $allErrors = $this->customErrors;

        foreach ($this->failures() as $failure) {
            $allErrors[] = "Row {$failure->row()}: " . implode(', ', $failure->errors());
        }

        return $allErrors;
    }

    /**
     * Get import summary
     */
    public function getSummary(): array
    {
        return [
            'processed' => $this->processedRows,
            'imported' => $this->importedCount,
            'skipped' => $this->skippedCount,
            'errors' => count($this->getErrors())
        ];
    }
}
