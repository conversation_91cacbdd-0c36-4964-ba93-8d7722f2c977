<?php

namespace Plugins\Business\Imports;

use Plugins\Business\Models\Business;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\SkipsErrors;
use Maatwebsite\Excel\Concerns\SkipsOnError;
use Maatwebsite\Excel\Concerns\SkipsFailures;
use Maatwebsite\Excel\Concerns\SkipsOnFailure;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class BusinessImport implements ToModel, WithHeadingRow, WithValidation, SkipsOnError, SkipsOnFailure
{
    use Importable, SkipsErrors, SkipsFailures;

    private $importedCount = 0;
    private $skippedCount = 0;
    private $processedRows = 0;
    private $customErrors = [];

    /**
     * Transform a row into a model
     */
    public function model(array $row)
    {
        $this->processedRows++;

        // Skip empty rows
        if (empty(array_filter($row))) {
            return null;
        }

        // Validate required fields
        if (empty($row['name']) || empty($row['email'])) {
            $this->customErrors[] = "Row " . ($this->processedRows + 1) . ": Missing required fields (name or email).";
            return null;
        }

        try {
            // Check if business already exists by email (primary check)
            $existingBusiness = Business::where('email', $row['email'])->first();

            // If not found by email, check by name as secondary check
            if (!$existingBusiness) {
                $existingBusiness = Business::where('name', $row['name'])->first();
            }

            if ($existingBusiness) {
                // Skip existing records silently (don't add to errors)
                $this->skippedCount++;
                return null;
            }

            // Create new business
            $business = new Business([
                'name' => $row['name'],
                'brand_name' => $row['brand_name'] ?? null,
                'legal_name' => $row['legal_name'] ?? null,
                'description' => $row['description'] ?? null,
                'email' => $row['email'],
                'phone' => $row['phone'] ?? null,
                'primary_phone' => $row['primary_phone'] ?? $row['phone'] ?? null,
                'address' => $row['address'] ?? null,
                'city' => $row['city'] ?? null,
                'state' => $row['state'] ?? null,
                'country' => $row['country'] ?? null,
                'postal_code' => isset($row['postal_code']) ? (string)$row['postal_code'] : null,
                'website' => $row['website'] ?? null,
                'website_url' => $row['website_url'] ?? $row['website'] ?? null,
                'tax_id' => isset($row['tax_id']) ? (string)$row['tax_id'] : null,
                'status' => $this->validateStatus($row['status'] ?? 'lead'),
                'created_by' => Auth::id(),
            ]);

            $this->importedCount++;
            return $business;

        } catch (\Exception $e) {
            $this->customErrors[] = "Row " . ($this->processedRows + 1) . ": " . $e->getMessage();
            return null;
        }
    }

    /**
     * Validation rules for each row
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'brand_name' => 'nullable|string|max:255',
            'legal_name' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'phone' => 'nullable|string|max:20',
            'primary_phone' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:500',
            'city' => 'nullable|string|max:100',
            'state' => 'nullable|string|max:100',
            'country' => 'nullable|string|max:100',
            'postal_code' => 'nullable|max:20', // Remove string requirement to handle numeric postal codes
            'website' => 'nullable|url|max:255',
            'website_url' => 'nullable|url|max:255',
            'tax_id' => 'nullable|max:50', // Remove string requirement to handle numeric tax IDs
            'status' => ['nullable', 'string', Rule::in(['lead', 'deal', 'customer', 'partner', 'churned', 'lost'])],
        ];
    }

    /**
     * Custom attributes for validation error messages
     */
    public function customValidationAttributes(): array
    {
        return [
            'name' => 'Business Name',
            'email' => 'Email Address',
            'brand_name' => 'Brand Name',
            'legal_name' => 'Legal Name',
            'phone' => 'Phone Number',
            'primary_phone' => 'Primary Phone',
            'website' => 'Website URL',
            'website_url' => 'Website URL',
            'tax_id' => 'Tax ID',
            'postal_code' => 'Postal Code',
        ];
    }

    /**
     * Validate and normalize status
     */
    private function validateStatus(?string $status): string
    {
        $validStatuses = ['lead', 'deal', 'customer', 'partner', 'churned', 'lost'];
        $status = strtolower(trim($status ?? 'lead'));
        
        return in_array($status, $validStatuses) ? $status : 'lead';
    }

    /**
     * Get the count of imported records
     */
    public function getImportedCount(): int
    {
        return $this->importedCount;
    }

    /**
     * Get the count of skipped records
     */
    public function getSkippedCount(): int
    {
        return $this->skippedCount;
    }

    /**
     * Get the count of processed rows
     */
    public function getProcessedRows(): int
    {
        return $this->processedRows;
    }

    /**
     * Get import errors
     */
    public function getErrors(): array
    {
        // Combine validation failures and custom errors
        $allErrors = $this->customErrors;

        foreach ($this->failures() as $failure) {
            $allErrors[] = "Row {$failure->row()}: " . implode(', ', $failure->errors());
        }

        return $allErrors;
    }

    /**
     * Get import summary
     */
    public function getSummary(): array
    {
        return [
            'processed' => $this->processedRows,
            'imported' => $this->importedCount,
            'skipped' => $this->skippedCount,
            'errors' => count($this->getErrors())
        ];
    }
}
