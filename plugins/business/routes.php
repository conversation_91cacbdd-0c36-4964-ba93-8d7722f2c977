<?php

use Illuminate\Support\Facades\Route;
use Plugins\Business\Controllers\BusinessController;
use Plugins\Business\Controllers\ContactController;
use Plugins\Business\Controllers\DocumentController;
use Plugins\Business\Controllers\ImportController;
use Plugins\Business\Controllers\BusinessActivityController;
use Plugins\Business\Controllers\BusinessNotificationController;
use Plugins\Business\Controllers\BusinessProductController;
use Plugins\Business\Controllers\BusinessTagController;
use Plugins\Business\Controllers\ProductController;
use Plugins\Business\Controllers\TagController;

// Main business routes
Route::prefix('business')->name('business.')->group(function () {
    // Static routes MUST come before dynamic parameter routes

    // Basic CRUD routes (static paths first)
    Route::get('/', [BusinessController::class, 'index'])->name('index');
    Route::get('/create', [BusinessController::class, 'create'])->name('create');
    Route::post('/', [BusinessController::class, 'store'])->name('store');

    // Import/Export routes (static paths)
    Route::prefix('import')->name('import.')->group(function () {
        Route::get('/', [ImportController::class, 'index'])->name('index');
        Route::post('/businesses', [ImportController::class, 'importBusinesses'])->name('businesses');
        Route::post('/contacts', [ImportController::class, 'importContacts'])->name('contacts');
        Route::get('/sample/businesses', [ImportController::class, 'downloadBusinessSample'])->name('sample.businesses');
        Route::get('/sample/contacts', [ImportController::class, 'downloadContactSample'])->name('sample.contacts');
    });

    // Dynamic parameter routes MUST come after all static routes
    Route::get('/{business}', [BusinessController::class, 'show'])->name('show');
    Route::get('/{business}/edit', [BusinessController::class, 'edit'])->name('edit');
    Route::put('/{business}', [BusinessController::class, 'update'])->name('update');
    Route::delete('/{business}', [BusinessController::class, 'destroy'])->name('destroy');

    // Business users management
    Route::get('/{business}/users', [BusinessController::class, 'users'])->name('users');
    Route::post('/{business}/users', [BusinessController::class, 'assignUser'])->name('assign-user');
    Route::delete('/{business}/users/{user}', [BusinessController::class, 'removeUser'])->name('remove-user');

    // Business reports
    Route::get('/{business}/reports', [BusinessController::class, 'reports'])->name('reports');

    // Business contacts
    Route::prefix('{business}/contacts')->name('contacts.')->group(function () {
        Route::get('/', [ContactController::class, 'index'])->name('index');
        Route::get('/create', [ContactController::class, 'create'])->name('create');
        Route::post('/', [ContactController::class, 'store'])->name('store');
        Route::get('/{contact}', [ContactController::class, 'show'])->name('show');
        Route::get('/{contact}/edit', [ContactController::class, 'edit'])->name('edit');
        Route::put('/{contact}', [ContactController::class, 'update'])->name('update');
        Route::delete('/{contact}', [ContactController::class, 'destroy'])->name('destroy');
        Route::post('/{contact}/set-primary', [ContactController::class, 'setPrimary'])->name('set-primary');
    });

    // Business documents
    Route::prefix('{business}/documents')->name('documents.')->group(function () {
        Route::get('/', [DocumentController::class, 'index'])->name('index');
        Route::get('/create', [DocumentController::class, 'create'])->name('create');
        Route::post('/', [DocumentController::class, 'store'])->name('store');
        Route::delete('/{document}', [DocumentController::class, 'destroy'])->name('destroy');
        Route::get('/{document}/download', [DocumentController::class, 'download'])->name('download');
        Route::get('/{document}/view', [DocumentController::class, 'view'])->name('view');
    });

    // Business activities
    Route::prefix('{business}/activities')->name('activities.')->group(function () {
        Route::get('/', [BusinessActivityController::class, 'index'])->name('index');
        Route::get('/create', [BusinessActivityController::class, 'create'])->name('create');
        Route::post('/', [BusinessActivityController::class, 'store'])->name('store');
        Route::get('/{activity}', [BusinessActivityController::class, 'show'])->name('show');
        Route::get('/{activity}/edit', [BusinessActivityController::class, 'edit'])->name('edit');
        Route::put('/{activity}', [BusinessActivityController::class, 'update'])->name('update');
        Route::delete('/{activity}', [BusinessActivityController::class, 'destroy'])->name('destroy');
    });

    // Business products
    Route::prefix('{business}/products')->name('products.')->group(function () {
        Route::get('/', [BusinessProductController::class, 'index'])->name('index');
        Route::post('/attach', [BusinessProductController::class, 'attach'])->name('attach');
        Route::delete('/{product}/detach', [BusinessProductController::class, 'detach'])->name('detach');
    });

    // Business tags
    Route::prefix('{business}/tags')->name('tags.')->group(function () {
        Route::get('/', [BusinessTagController::class, 'index'])->name('index');
        Route::post('/attach', [BusinessTagController::class, 'attach'])->name('attach');
        Route::delete('/{tag}/detach', [BusinessTagController::class, 'detach'])->name('detach');
    });

    // Business notifications
    Route::prefix('{business}/notifications')->name('notifications.')->group(function () {
        Route::get('/', [BusinessNotificationController::class, 'index'])->name('index');
        Route::get('/preferences', [BusinessNotificationController::class, 'showPreferences'])->name('preferences.show');
        Route::post('/preferences', [BusinessNotificationController::class, 'updatePreferences'])->name('update-preferences');
    });
});

// Global contacts routes
Route::get('/contacts', [ContactController::class, 'globalIndex'])->name('contacts.index');
Route::get('/contacts/global', [ContactController::class, 'globalIndex'])->name('contacts.global');
Route::get('/contacts/{contact}/edit', [ContactController::class, 'globalEdit'])->name('contacts.edit');
Route::put('/contacts/{contact}', [ContactController::class, 'globalUpdate'])->name('contacts.update');
Route::delete('/contacts/{contact}', [ContactController::class, 'globalDestroy'])->name('contacts.destroy');

// Global documents route
Route::get('/documents', [DocumentController::class, 'globalIndex'])->name('documents.global');

// Global products routes (if needed)
Route::prefix('products')->name('products.')->group(function () {
    Route::get('/', [ProductController::class, 'index'])->name('index');
    Route::get('/create', [ProductController::class, 'create'])->name('create');
    Route::post('/', [ProductController::class, 'store'])->name('store');
    Route::get('/{product}', [ProductController::class, 'show'])->name('show');
    Route::get('/{product}/edit', [ProductController::class, 'edit'])->name('edit');
    Route::put('/{product}', [ProductController::class, 'update'])->name('update');
    Route::delete('/{product}', [ProductController::class, 'destroy'])->name('destroy');
});

// Global tags routes (if needed)
Route::prefix('tags')->name('tags.')->group(function () {
    Route::get('/', [TagController::class, 'index'])->name('index');
    Route::get('/create', [TagController::class, 'create'])->name('create');
    Route::post('/', [TagController::class, 'store'])->name('store');
    Route::get('/{tag}', [TagController::class, 'show'])->name('show');
    Route::get('/{tag}/edit', [TagController::class, 'edit'])->name('edit');
    Route::put('/{tag}', [TagController::class, 'update'])->name('update');
    Route::delete('/{tag}', [TagController::class, 'destroy'])->name('destroy');
});
